import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export function generateShareableLink() {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}

export function formatCurrency(amount) {
  // Ensure amount is a positive number
  const numAmount = Math.abs(Number(amount) || 0);
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numAmount);
}

export function formatDate(date) {
  return new Intl.DateTimeFormat("en-IN", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(new Date(date));
}

export function formatPercentage(percentage) {
  const num = parseFloat(percentage);
  if (isNaN(num)) return "0";

  // If the number is less than 1, it might be stored as decimal (0.085 for 8.5%)
  // Convert it to percentage by multiplying by 100
  if (num < 1 && num > 0) {
    return (num * 100).toString();
  }

  // Otherwise, return as-is (already in percentage form)
  return num.toString();
}
