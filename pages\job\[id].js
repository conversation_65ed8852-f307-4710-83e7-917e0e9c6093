import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Layout from "../../components/Layout";
import { formatCurrency, formatDate } from "../../lib/utils";
import {
  Calendar,
  MapPin,
  DollarSign,
  Briefcase,
  Globe,
  Github,
  Linkedin,
  FileText,
  User,
  Building,
  Eye,
} from "lucide-react";

export default function JobDetail({ user }) {
  const router = useRouter();
  const { id } = router.query;
  const [job, setJob] = useState(null);
  const [loading, setLoading] = useState(true);
  const [applying, setApplying] = useState(false);
  const [applicationForm, setApplicationForm] = useState({
    message: "",
    companyName: "",
    positionDetails: "",
  });

  useEffect(() => {
    if (id) {
      fetchJob();
    }
  }, [id]);

  const fetchJob = async () => {
    try {
      const res = await fetch(`/api/jobs/${id}`);
      const data = await res.json();
      setJob(data.job);
    } catch (error) {
      console.error("Failed to fetch job:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleApply = async (e) => {
    e.preventDefault();

    if (!user) {
      router.push("/login");
      return;
    }

    if (user.user_type !== "referrer") {
      alert("Only referrers can apply to job posts");
      return;
    }

    setApplying(true);

    try {
      const res = await fetch("/api/applications/apply", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          jobPostId: job.id,
          ...applicationForm,
        }),
      });

      if (res.ok) {
        alert("Application submitted successfully!");
        router.push("/dashboard");
      } else {
        const error = await res.json();
        alert(error.error || "Failed to submit application");
      }
    } catch (error) {
      alert("Failed to submit application");
    } finally {
      setApplying(false);
    }
  };

  if (loading) {
    return (
      <Layout user={user}>
        <div className="min-h-screen flex items-center justify-center">
          <div>Loading...</div>
        </div>
      </Layout>
    );
  }

  if (!job) {
    return (
      <Layout user={user}>
        <div className="min-h-screen flex items-center justify-center">
          <div>Job not found</div>
        </div>
      </Layout>
    );
  }

  const isOwner = user && user.id === job.user_id;
  const canApply = user && user.user_type === "referrer" && !isOwner;

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-lg p-8">
            {/* Header */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-2">
                <h1 className="text-3xl font-bold">{job.title}</h1>
                {job.visibility === "anonymous" && (
                  <Eye className="text-gray-400" size={20} />
                )}
              </div>
              <div className="flex flex-wrap gap-4 text-gray-600">
                <div className="flex items-center gap-1">
                  <User size={16} />
                  <span>{job.user_name}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar size={16} />
                  <span>{formatDate(job.created_at)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Briefcase size={16} />
                  <span>{job.job_role}</span>
                </div>
              </div>
            </div>

            {/* Payment Details */}
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h2 className="text-lg font-semibold mb-3">Referrer Compensation</h2>
              <div className="flex items-center gap-2 text-2xl font-bold text-green-600">
                <DollarSign size={24} />
                {job.payment_type === "percentage"
                  ? `${job.payment_percentage}% of monthly salary (recurring)`
                  : formatCurrency(job.payment_fixed)}
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                {job.payment_type === "percentage"
                  ? "The job seeker will pay this percentage of their monthly salary to the successful referrer."
                  : "The job seeker will pay this fixed amount to the successful referrer upon job placement."}
              </p>
            </div>

            {/* Description */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-3">Description</h2>
              <p className="text-gray-700 whitespace-pre-wrap">
                {job.description}
              </p>
            </div>

            {/* Additional Details */}
            {job.skills && (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-3">Required Skills</h2>
                <p className="text-gray-700">{job.skills}</p>
              </div>
            )}

            {job.experience_years && (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-3">
                  Experience Required
                </h2>
                <p className="text-gray-700">{job.experience_years} years</p>
              </div>
            )}

            {job.desired_companies && (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-3">Target Companies</h2>
                <p className="text-gray-700">{job.desired_companies}</p>
              </div>
            )}

            {/* User Links */}
            {job.visibility !== "anonymous" && (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-3">Connect</h2>
                <div className="flex gap-4">
                  {job.linkedin_url && (
                    <a
                      href={job.linkedin_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-blue-600 hover:underline"
                    >
                      <Linkedin size={20} />
                      LinkedIn
                    </a>
                  )}
                  {job.github_url && (
                    <a
                      href={job.github_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-gray-800 hover:underline"
                    >
                      <Github size={20} />
                      GitHub
                    </a>
                  )}
                  {job.portfolio_url && (
                    <a
                      href={job.portfolio_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-purple-600 hover:underline"
                    >
                      <Globe size={20} />
                      Portfolio
                    </a>
                  )}
                </div>
              </div>
            )}

            {/* Apply Form */}
            {canApply && (
              <div className="border-t pt-6">
                <h2 className="text-lg font-semibold mb-4">Apply to Refer</h2>
                <form onSubmit={handleApply} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Message to Job Seeker
                    </label>
                    <textarea
                      required
                      rows={4}
                      value={applicationForm.message}
                      onChange={(e) =>
                        setApplicationForm({
                          ...applicationForm,
                          message: e.target.value,
                        })
                      }
                      className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                      placeholder="Explain why you're a good fit to refer this person..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Company Name (if you have a specific opportunity)
                    </label>
                    <input
                      type="text"
                      value={applicationForm.companyName}
                      onChange={(e) =>
                        setApplicationForm({
                          ...applicationForm,
                          companyName: e.target.value,
                        })
                      }
                      className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Position Details (optional)
                    </label>
                    <textarea
                      rows={3}
                      value={applicationForm.positionDetails}
                      onChange={(e) =>
                        setApplicationForm({
                          ...applicationForm,
                          positionDetails: e.target.value,
                        })
                      }
                      className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                      placeholder="Any specific details about the position..."
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={applying}
                    className="w-full py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
                  >
                    {applying ? "Submitting..." : "Submit Application"}
                  </button>
                </form>
              </div>
            )}

            {isOwner && (
              <div className="border-t pt-6">
                <p className="text-gray-600 text-center">
                  This is your job post. You have received{" "}
                  {job.applications_count} applications.
                </p>
              </div>
            )}

            {!user && (
              <div className="border-t pt-6">
                <p className="text-center text-gray-600">
                  Please{" "}
                  <a
                    href="/login"
                    className="text-black font-medium hover:underline"
                  >
                    login
                  </a>{" "}
                  to apply for this opportunity.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
